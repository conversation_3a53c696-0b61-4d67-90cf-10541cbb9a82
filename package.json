{"name": "client-mvp-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev:host": "next dev -H client-mvp-local.mobilversichert-dev.de --turbopack", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "docker:dev": "docker-compose -f .docker/local/docker-compose.yml up --build", "docker:prod": "docker-compose -f .docker/production/docker-compose.yml up --build", "docker:watch": "docker-compose -f .docker/local/docker-compose.yml watch", "docker:stop": "docker-compose -f .docker/local/docker-compose.yml down || docker-compose -f .docker/production/docker-compose.yml down || true", "docker:fresh": "npm run docker:stop && docker system prune -f"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-virtual": "^3.13.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.12.1", "immer": "^10.1.1", "input-otp": "^1.4.2", "jose": "^6.0.11", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "next": "15.3.2", "next-intl": "^4.1.0", "next-safe-action": "^7.10.8", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "qs": "^6.14.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-pdf": "^9.2.1", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^3.0.2", "react-signature-canvas": "^1.1.0-alpha.2", "recharts": "^2.15.3", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "xior": "^0.7.8", "zod": "^3.24.4", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/qs": "^6.14.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-signature-canvas": "^1.0.7", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}