import { FamilyStatusType } from '@/modules/profile/types/profile-schema'

import { GenderType } from './auth-schema'

export interface LoginResponse {
  access_token: string
  expires_in: number
  token_type: string
  refresh_token: string
}

export interface AuthSession {
  accessToken: string
  refreshToken: string
  expiresAt: number
  expiresIn: number
  isExpired?: boolean
  user: {
    id: string
    email: string
    agentId: string
    agencyId: string
  }
}

export interface UserProfileResponse {
  _embedded: {
    profile: UserProfile[]
  }
}

export type AcceptanceStatus = 'REQUESTED' | 'AUTHORIZED'

export interface UserProfile {
  id: string
  status: string
  statusReason: string | null
  agentId: string
  agentName: string
  agencyId: string
  agencyName: string
  agencyCity: string
  agencyStreet: string
  agencyStreetNumber: string
  agencyCountry: string
  agencyZip: string
  imgId: string
  age: number | null
  birthdate: string | null
  birthPlace: string | null
  birthName: string | null
  gender: string
  salutation: GenderType
  title: string | null
  firstName: string
  lastName: string
  street: string
  streetNum: string
  zip: string
  city: string
  country: string
  nationality: string
  phone: string | null
  mobile: string | null
  email: string
  familyStatus: FamilyStatusType
  children: number
  profession: string | null
  jobType: string | null
  official: string
  workPercentagePhysical: number
  workPercentageOffice: number
  incomeYearlyBrutto: number
  incomeYearlyNetto: number
  incomeYearlySalaries: number
  capitalFormingPayments: number
  incomeCurrency: string
  taxOfficeName: string | null
  taxNumber: string | null
  taxId: string | null
  taxClass: string
  churchTaxPercentage: number
  retirementSavingSince: string
  healthInsurance: string | null
  socialInsuranceNumber: string | null
  weight: number
  height: number
  smoker: boolean | null
  healthInfo: string | null
  completion: number
  signal: string | null
  contracts: number
  shares: number
  lastContact: string | null
  picture: string | null
  extId: string | null
  extStatus: string
  extModified: string | null
  extNumber: string | null
  emailValidation: string
  mobileValidation: string
  passwordValidation: string
  documentIds: string | null
  agencyImgId: string
  invitationStatus: string
  invitationTime: string
  invitationCount: number
  webInvitationStatus: string
  webInvitationTime: string
  webInvitationCount: number
  hasPushNotification: number
  acceptanceStatus: AcceptanceStatus
  acceptanceTime: string | null
  acceptanceId: string | null
  feeling: string | null
  score: number | null
  hasCalculator: boolean | null
  signedLegalProtection: boolean
  primaryPaymentInfoDetails: string | null
  primaryPaymentInfoIsPrimary: boolean | null
  primaryPaymentInfoSource: string | null
  primaryPaymentInfoStatus: string | null
  acceptanceDocumentUrl?: string | null
}
