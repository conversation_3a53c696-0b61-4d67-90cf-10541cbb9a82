import { ClientDataManagement } from '../../../types/data-management-types'
import { BaseStepForm } from '../../base-step-form'
import { DataManagementNavigation } from '../../data-management-navigation'
import { HEALTH_AND_RISKS_STEP } from './health-and-risks-step'

interface HealthAndRisksFormProps {
  clientDataManagement: ClientDataManagement
}

export const HealthAndRisksForm = ({ clientDataManagement }: HealthAndRisksFormProps) => {
  // Get existing health and risks data if available
  const existingHealthAndRisks = clientDataManagement?.steps?.healthAndRisks

  return (
    <BaseStepForm
      clientDataManagement={clientDataManagement}
      step={HEALTH_AND_RISKS_STEP}
      existingData={existingHealthAndRisks}
      formOptions={{
        submitButtonText: 'Save Health Information',
      }}
      navigationComponent={(packageProps) => (
        <DataManagementNavigation {...packageProps} clientDataManagement={clientDataManagement} />
      )}
      componentName="HealthAndRisksForm"
      dataKey="healthAndRisks"
    />
  )
}
