import { StepConfig } from '@/components/dynamic-form'

import { createField } from '../../../libs/steps/field-helpers'

export const HEALTH_AND_RISKS_STEP: StepConfig = {
  id: 'healthAndRisks',
  title: 'Health & Risks',
  description: 'Provide health and risk information for the client',
  columns: 2,
  fields: [
    createField({
      name: 'weight',
      type: 'input',
      label: 'HEALTH_AND_RISKS.EDIT.WEIGHT_FIELD.LABEL',
    }),
    createField({
      name: 'height',
      type: 'input',
      label: 'HEALTH_AND_RISKS.EDIT.HEIGHT_FIELD.LABEL',
    }),
    createField({
      name: 'smoker',
      type: 'select',
      label: 'HEALTH_AND_RISKS.EDIT.SMOKER_FIELD.LABEL',
      options: [
        { value: 'no', label: 'No' },
        { value: 'sometimes', label: 'Sometimes' },
        { value: 'yes', label: 'Yes' },
      ],
    }),
    createField({
      name: 'healthInfo',
      type: 'textarea',
      label: 'HEALTH_AND_RISKS.EDIT.HEALTH_INFO_FIELD.LABEL',
    }),
  ],
}
